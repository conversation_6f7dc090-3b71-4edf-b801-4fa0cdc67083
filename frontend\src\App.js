import React, { useState } from 'react';
import './App.css';

function App() {
  const [summonerName, setSummonerName] = useState('');
  const [tagLine, setTagLine] = useState('');
  const [summonerData, setSummonerData] = useState(null);
  const [matchHistory, setMatchHistory] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSummonerData(null);
    setMatchHistory([]);

    try {
      const summonerRes = await fetch(`http://localhost:5001/api/summoner/${summonerName}/${tagLine}`);
      if (!summonerRes.ok) throw new Error('Summoner not found');
      const summoner = await summonerRes.json();
      setSummonerData(summoner);

      const matchesRes = await fetch(`http://localhost:5001/api/matches/${summonerName}/${tagLine}`);
      if (!matchesRes.ok) throw new Error('Could not fetch match history');
      const matches = await matchesRes.json();
      setMatchHistory(matches);

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Riot API Frontend</h1>
      </header>
      <main>
        <form onSubmit={handleSearch} className="search-form">
          <input 
            type="text" 
            placeholder="Summoner Name" 
            value={summonerName} 
            onChange={(e) => setSummonerName(e.target.value)} 
            required 
          />
          <select 
            value={tagLine} 
            onChange={(e) => setTagLine(e.target.value)} 
            required
          >
            <option value="">Select Region</option>
            <option value="NA1">NA1 - North America</option>
            <option value="EUW1">EUW1 - Europe West</option>
            <option value="EUN1">EUN1 - Europe Nordic & East</option>
            <option value="KR">KR - Korea</option>
            <option value="JP1">JP1 - Japan</option>
            <option value="BR1">BR1 - Brazil</option>
            <option value="LA1">LA1 - Latin America North</option>
            <option value="LA2">LA2 - Latin America South</option>
            <option value="OC1">OC1 - Oceania</option>
            <option value="TR1">TR1 - Turkey</option>
            <option value="RU">RU - Russia</option>
            <option value="PH2">PH2 - Philippines</option>
            <option value="SG2">SG2 - Singapore</option>
            <option value="TH2">TH2 - Thailand</option>
            <option value="TW2">TW2 - Taiwan</option>
            <option value="VN2">VN2 - Vietnam</option>
          </select>
          <button type="submit" disabled={loading}>{loading ? 'Searching...' : 'Search'}</button>
        </form>

        {error && <p className="error">{error}</p>}

        {summonerData && (
          <div className="summoner-info card">
            <h2>{summonerData.name}</h2>
            <p>Level: {summonerData.summonerLevel}</p>
            <img 
              src={`http://ddragon.leagueoflegends.com/cdn/11.23.1/img/profileicon/${summonerData.profileIconId}.png`} 
              alt="Profile Icon" 
              width="100"
              style={{borderRadius: '50%'}}
            />
          </div>
        )}

        {matchHistory.length > 0 && (
          <div className="match-history card">
            <h2>Recent Matches</h2>
            {matchHistory.map((match, index) => (
              <div key={index} className={`match-item ${match.win ? 'win' : 'loss'}`}>
                <h3>{match.win ? 'Victory' : 'Defeat'} in {match.gameMode}</h3>
                <p>Champion: {match.champion}</p>
                <p>KDA: {match.kills}/{match.deaths}/{match.assists} ({match.kda})</p>
                <p>Duration: {Math.floor(match.gameDuration / 60)} minutes</p>
                <h4>Participants:</h4>
                <ul>
                  {match.participants.map((p, i) => <li key={i}>{p}</li>)}
                </ul>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}

export default App;

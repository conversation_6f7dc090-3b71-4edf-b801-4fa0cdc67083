import requests
from dotenv import load_dotenv
import os

# Load API key from environment or .env file
load_dotenv()
API_KEY = os.getenv("RIOT_API_KEY")
if not API_KEY:
    raise ValueError("RIOT_API_KEY not found in environment or .env file")

def get_puuid(summoner_name, tag_line):
    """Get PUUID for a summoner by their Riot ID"""
    headers = {"X-Riot-Token": API_KEY}
    
    # Get account info (PUUID)
    account_url = f"https://americas.api.riotgames.com/riot/account/v1/accounts/by-riot-id/{summoner_name}/{tag_line}"
    response = requests.get(account_url, headers=headers)
    response.raise_for_status()
    account_data = response.json()
    puuid = account_data['puuid']
    
    return puuid

def get_summoner_info(summoner_name, tag_line):
    """Fetch summoner info from Riot API using summoner name and tag line."""
    headers = {"X-Riot-Token": API_KEY}
    
    # Get PUUID using the new function
    puuid = get_puuid(summoner_name, tag_line)
    
    # Get summoner details
    summoner_url = f"https://na1.api.riotgames.com/lol/summoner/v4/summoners/by-puuid/{puuid}"
    response = requests.get(summoner_url, headers=headers)
    response.raise_for_status()
    summoner_data = response.json()
    
    return summoner_data

def get_match_history(summoner_name, tag_line, count=5):
    """Get recent match history for a summoner"""
    headers = {"X-Riot-Token": API_KEY}
    
    # Get PUUID
    puuid = get_puuid(summoner_name, tag_line)
    
    # Get match IDs (using americas routing for match-v5)
    match_ids_url = f"https://americas.api.riotgames.com/lol/match/v5/matches/by-puuid/{puuid}/ids"
    params = {"count": count}
    
    response = requests.get(match_ids_url, headers=headers, params=params)
    response.raise_for_status()
    match_ids = response.json()
    
    # Get detailed match data for each match
    matches = []
    for match_id in match_ids:
        match_url = f"https://americas.api.riotgames.com/lol/match/v5/matches/{match_id}"
        match_response = requests.get(match_url, headers=headers)
        match_response.raise_for_status()
        match_data = match_response.json()
        matches.append(match_data)
    
    return matches


def get_player_match_stats(match_data, puuid):
    """Extract specific player's stats from a match"""
    participants = match_data['info']['participants']
    
    # Find the player's data in the match
    player_data = None
    for participant in participants:
        if participant['puuid'] == puuid:
            player_data = participant
            break
    
    if not player_data:
        return None
    
    # Extract key stats
    stats = {
        'champion': player_data['championName'],
        'damageDealt': player_data['totalDamageDealtToChampions'],
        'damageTaken': player_data['totalDamageTaken'],
        'goldEarned': player_data['goldEarned'],
        'visionScore': player_data['visionScore'],
        'kills': player_data['kills'],
        'deaths': player_data['deaths'],
        'assists': player_data['assists'],
        'win': player_data['win'],
        'gameMode': match_data['info']['gameMode'],
        'gameDuration': match_data['info']['gameDuration'],
        'kda': round((player_data['kills'] + player_data['assists']) / max(player_data['deaths'], 1), 2)
    }
    
    return stats

def get_summoners_in_match(match_data):
    """Returns list of Riot IDs for all players in a match."""
    participants = match_data.get("info", {}).get("participants", [])

    summoners = []
    for p in participants:
        game_name = p.get("riotIdGameName")
        tag_line = p.get("riotIdTagline")

        # Newer matches have the Riot ID split into game name & tag line
        if game_name and tag_line:
            summoners.append(f"{game_name}#{tag_line}")
        else:
            # Fallback for older data – use the in-client summoner name
            summoners.append(p.get("summonerName", "Unknown"))

    return summoners

def validate_summoner_exists(summoner_name, tag_line):
    """Check if summoner name/tag combination is valid"""
    try:
        get_puuid(summoner_name, tag_line)
        return True
    except requests.exceptions.RequestException:
        return False

def batch_summoner_lookup(summoner_list):
    """Look up multiple summoners efficiently"""
    results = {}
    
    for summoner_entry in summoner_list:
        if '#' in summoner_entry:
            summoner_name, tag_line = summoner_entry.split('#', 1)
        else:
            # Assume NA1 if no tag provided
            summoner_name = summoner_entry
            tag_line = "NA1"
        
        try:
            summoner_info = get_summoner_info(summoner_name, tag_line)
            results[summoner_entry] = summoner_info
        except requests.exceptions.RequestException as e:
            results[summoner_entry] = {"error": str(e)}
    
    return results

def get_champion_mastery(summoner_name, tag_line, champion_id=None):
    """Get champion mastery data for a summoner

    Args:
        summoner_name: Riot ID game name
        tag_line: Riot ID tag line
        champion_id: Optional specific champion ID. If None, returns all champions

    Returns:
        List of champion mastery data or single champion data if champion_id specified
    """
    headers = {"X-Riot-Token": API_KEY}

    # Get summoner info to get the encrypted summoner ID
    summoner_data = get_summoner_info(summoner_name, tag_line)
    encrypted_summoner_id = summoner_data['id']

    # Build the mastery URL
    if champion_id:
        # Get mastery for specific champion
        mastery_url = f"https://na1.api.riotgames.com/lol/champion-mastery/v4/champion-masteries/by-summoner/{encrypted_summoner_id}/by-champion/{champion_id}"
    else:
        # Get all champion masteries
        mastery_url = f"https://na1.api.riotgames.com/lol/champion-mastery/v4/champion-masteries/by-summoner/{encrypted_summoner_id}"

    response = requests.get(mastery_url, headers=headers)
    response.raise_for_status()
    mastery_data = response.json()

    # If getting all champions, sort by mastery points (highest first)
    if not champion_id and isinstance(mastery_data, list):
        mastery_data.sort(key=lambda x: x['championPoints'], reverse=True)

    return mastery_data

def get_server_status():
    """Check Riot API server status"""
    headers = {"X-Riot-Token": API_KEY}

    try:
        # Use a simple API endpoint to check if service is up
        test_url = "https://na1.api.riotgames.com/lol/status/v4/platform-data"
        response = requests.get(test_url, headers=headers, timeout=10)

        if response.status_code == 200:
            return {"status": "online", "response_time": response.elapsed.total_seconds()}
        else:
            return {"status": "degraded", "status_code": response.status_code}
    except requests.exceptions.RequestException as e:
        return {"status": "offline", "error": str(e)}

#main function
if __name__ == "__main__":
    summoner_name = "Rareterrance"
    tag_line = "NA1"
    
    # Get summoner info
    summoner = get_summoner_info(summoner_name, tag_line)
    print(f"=== {summoner_name}#{tag_line} ===")
    print(f"Level: {summoner['summonerLevel']}")
    print(f"Icon: {summoner['profileIconId']}")
    print(f"PUUID: {summoner['puuid']}")
    
    # Get match history
    print(f"\n=== Recent Match History ===")
    try:
        matches = get_match_history(summoner_name, tag_line, count=5)
        puuid = get_puuid(summoner_name, tag_line)
        
        for i, match in enumerate(matches, 1):
            stats = get_player_match_stats(match, puuid)
            if stats:
                result = "WIN" if stats['win'] else "LOSS"
                duration_min = stats['gameDuration'] // 60
                print(f"\nMatch {i}: {result}")
                print(f"  Champion: {stats['champion']}")
                print(f"  Damage Dealt: {stats['damageDealt']}")
                print(f"  Damage Taken: {stats['damageTaken']}")
                print(f"  Gold Earned: {stats['goldEarned']}")
                print(f"  Vision Score: {stats['visionScore']}")
                print(f"  KDA: {stats['kills']}/{stats['deaths']}/{stats['assists']} (Ratio: {stats['kda']})")
                print(f"  Game Mode: {stats['gameMode']}")
                print(f"  Duration: {duration_min} minutes")
                
                # Get summoner names in the match
                summoner_names = get_summoners_in_match(match)
                print(f"\n=== Participants in Match {i} ===")
                for name in summoner_names:
                    print(f"- {name}")

    except Exception as e:
        print(f"Error fetching match history: {e}")
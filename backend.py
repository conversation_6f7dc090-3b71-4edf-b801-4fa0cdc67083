from flask import Flask, jsonify
from flask_cors import CORS
import riot_api

app = Flask(__name__)
CORS(app)  # This will allow the frontend to make requests to this backend

@app.route('/api/summoner/<string:summoner_name>/<string:tag_line>', methods=['GET'])
def get_summoner(summoner_name, tag_line):
    try:
        summoner_info = riot_api.get_summoner_info(summoner_name, tag_line)
        return jsonify(summoner_info)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/matches/<string:summoner_name>/<string:tag_line>', methods=['GET'])
def get_matches(summoner_name, tag_line):
    try:
        # We'll also get the puuid to process the match stats
        puuid = riot_api.get_puuid(summoner_name, tag_line)
        matches = riot_api.get_match_history(summoner_name, tag_line, count=5)
        
        match_stats_list = []
        for match in matches:
            stats = riot_api.get_player_match_stats(match, puuid)
            if stats:
                # We'll add participants to the stats payload
                stats['participants'] = riot_api.get_summoners_in_match(match)
                match_stats_list.append(stats)

        return jsonify(match_stats_list)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5001) # Running on a different port than the React app

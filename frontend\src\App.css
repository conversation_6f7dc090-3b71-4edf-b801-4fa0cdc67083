body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #121212;
  color: #e0e0e0;
}

.App {
  text-align: center;
}

.App-header {
  background-color: #1f1f1f;
  padding: 20px;
  border-bottom: 1px solid #333;
}

.App-header h1 {
  margin: 0;
  font-size: 2rem;
  color: #bb86fc;
}

main {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.search-form {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
}

.search-form input,
.search-form select {
  padding: 10px 15px;
  font-size: 1rem;
  border-radius: 5px;
  border: 1px solid #333;
  background-color: #1f1f1f;
  color: #e0e0e0;
}

.search-form select {
  min-width: 200px;
}

.search-form select option {
  background-color: #1f1f1f;
  color: #e0e0e0;
}

.search-form button {
  padding: 10px 20px;
  font-size: 1rem;
  border-radius: 5px;
  border: none;
  background-color: #bb86fc;
  color: #121212;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-form button:hover {
  background-color: #a36ef4;
}

.card {
  background-color: #1f1f1f;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.card h2 {
  margin-top: 0;
  color: #bb86fc;
}

.match-history .match-item {
  border: 1px solid #444;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 15px;
  text-align: left;
}

.match-item.win {
  border-left: 5px solid #03dac6; /* Teal for win */
}

.match-item.loss {
  border-left: 5px solid #cf6679; /* Red for loss */
}

.match-item h3 {
  margin-top: 0;
}

.match-item.win h3 {
  color: #03dac6;
}

.match-item.loss h3 {
  color: #cf6679;
}

.match-item ul {
  padding-left: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 5px;
}

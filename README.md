# 🎮 Simple Riot API Project

Finally, a Riot API script that **just works** without all the complexity!

## 🚀 Quick Start (2 minutes)

1. **Install requests**: `pip install requests`

2. **Get your API key**:
   - Go to https://developer.riotgames.com/
   - Sign in and click "REGENERATE API KEY"
   - Copy the key (starts with `RGAPI-`)

3. **Edit the script**:
   - Open `riot_api.py`
   - Replace `"YOUR_API_KEY"` with your actual key

4. **Run it**: `python riot_api.py`

That's it! 🎉

## 💡 What This Does

- Tests your API key
- Looks up a summoner (default: Doublelift)
- Shows their level and profile icon
- Gives you helpful error messages

## 🔧 If Something Goes Wrong

**❌ "API key expired"**: Keys expire every 24 hours, just regenerate a new one

**❌ "Invalid API key"**: Make sure you copied the full key correctly

**❌ "Summoner not found"**: The default test uses "Doublelift#NA1" which should always work

## 📝 What's Next?

Once this works, you can:
- Change the summoner name to test other players
- Add more API calls (ranked stats, match history, etc.)
- Build whatever you want!

---

*Made simple because the Riot API shouldn't be this hard to get started with* 😅 